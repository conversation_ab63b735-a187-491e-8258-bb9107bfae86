"""
Fyers API client for fetching market data.
Handles authentication and provides methods to get quotes for symbols.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from app.integrations.fyers.fyers_config import FyersConfig
import time
import pytz
from app.core.config_loader import get_config

logger = logging.getLogger(__name__)

# Interval mapping for Fyers API
INTERVAL_MAP = {
    "1D": "1D",  # Days
    "1W": "7D",  # Days
    "1": "1",    # Minutes
    "3": "3",
    "5": "5",
    "15": "15",
    "30": "30",
    "60": "60"   # Minutes
}

@dataclass
class MarketData:
    """Data class to represent market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float

@dataclass
class OHLCData:
    """Data class to represent OHLC historical data."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int

class FyersClient:
    """Client for interacting with Fyers API."""
    
    def __init__(self, env_path: str = None):
        """
        Initialize Fyers client.
        
        Args:
            env_path: Path to environment file containing Fyers credentials
        """
        self.fyers_config = FyersConfig(env_path=env_path)
        self.fyers_api = None
        self.access_token = None
        
    def authenticate(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication...")
            self.access_token = self.fyers_config.authenticate()
            
            if self.access_token:
                # Initialize Fyers API client
                from fyers_apiv3.fyersModel import FyersModel
                
                self.fyers_api = FyersModel(
                    client_id=self.fyers_config.config["client_id"],
                    is_async=False,
                    token=self.access_token
                )
                
                logger.info("Fyers authentication successful")
                return True
            else:
                logger.error("Failed to get access token")
                return False
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, MarketData]:
        """
        Get market quotes for multiple symbols with rate limiting and retry logic.

        Args:
            symbols: List of symbol strings in NSE format

        Returns:
            Dictionary mapping symbol to MarketData object
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return {}

        # Memory optimization: Early return for empty symbol list
        if not symbols:
            return {}

        # Get rate limiting configuration
        config = get_config()
        min_delay = config.rate_limit.min_delay_seconds
        max_retries = config.rate_limit.max_retries
        retry_backoff = config.rate_limit.retry_backoff

        # Memory optimization: Pre-allocate dictionary with expected size
        market_data = {}

        # Performance optimization: Use larger batch size for better throughput
        # but respect API limits
        batch_size = 50  # Keep at 50 as per Fyers API recommendation
        total_batches = (len(symbols) + batch_size - 1) // batch_size

        # Performance tracking
        start_time = time.time()
        successful_batches = 0
        failed_batches = 0

        try:
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                batch_num = i // batch_size + 1

                # Optimized logging: Log every 25 batches for large datasets, or key milestones
                should_log = (
                    batch_num % 25 == 1 or
                    batch_num == total_batches or
                    batch_num in [10, 50, 100, 200] or
                    total_batches <= 10
                )

                if should_log:
                    elapsed = time.time() - start_time
                    rate = batch_num / elapsed if elapsed > 0 else 0
                    eta = (total_batches - batch_num) / rate if rate > 0 else 0
                    logger.info(f"Batch {batch_num}/{total_batches} ({len(batch_symbols)} symbols) - "
                              f"Rate: {rate:.1f} batches/sec, ETA: {eta:.0f}s")

                # Retry logic for rate limiting
                attempt = 0
                batch_success = False

                while attempt <= max_retries and not batch_success:

                    try:
                        # Prepare symbols for Fyers API (comma-separated)
                        symbol_string = ",".join(batch_symbols)

                        # Get quotes from Fyers API
                        response = self.fyers_api.quotes({"symbols": symbol_string})

                        if response and response.get("code") == 200:
                            quotes_data = response.get("d", {})
                            batch_success = True
                            successful_batches += 1

                            # Optimized data processing
                            self._process_quotes_data(quotes_data, batch_symbols, market_data)

                            # Respect rate limit after successful request
                            time.sleep(min_delay)

                        elif response and response.get("code") == 429:
                            # Rate limit hit - retry with exponential backoff
                            attempt += 1
                            if attempt <= max_retries:
                                wait_time = retry_backoff * (2 ** (attempt - 1))  # Exponential backoff
                                if should_log or attempt == 1:
                                    logger.warning(f"Rate limit hit for batch {batch_num}, attempt {attempt}/{max_retries}. "
                                                 f"Retrying after {wait_time:.1f} seconds...")
                                time.sleep(wait_time)
                            else:
                                logger.error(f"Exceeded max retries for batch {batch_num} due to rate limiting")
                                failed_batches += 1
                                break
                        else:
                            # If bad request, try to find the bad symbol(s)
                            if response and response.get("code") == -99 and response.get("message", "").lower() == "bad request":
                                logger.error(f"Batch {batch_num} failed with bad request. Attempting to isolate bad symbol(s)...")
                                bad_symbols = []
                                for symbol in batch_symbols:
                                    try:
                                        resp = self.fyers_api.quotes({"symbols": symbol})
                                        if not (resp and resp.get("code") == 200):
                                            logger.error(f"Symbol failed: {symbol} | Response: {resp}")
                                            bad_symbols.append(symbol)
                                    except Exception as e:
                                        logger.error(f"Error fetching symbol {symbol}: {e}")
                                        bad_symbols.append(symbol)
                                if bad_symbols:
                                    logger.warning(f"Omitting {len(bad_symbols)} bad symbol(s) from batch {batch_num} and retrying batch.")
                                    batch_symbols = [s for s in batch_symbols if s not in bad_symbols]
                                    if not batch_symbols:
                                        logger.error(f"All symbols in batch {batch_num} are invalid. Skipping batch.")
                                        failed_batches += 1
                                        break
                                    # Reset attempt counter to retry with new batch
                                    attempt = 0
                                    continue
                                else:
                                    logger.error(f"Could not isolate bad symbol(s) in batch {batch_num}. Skipping batch.")
                                    failed_batches += 1
                                    break
                            else:
                                logger.error(f"Failed to get quotes for batch {batch_num}: {response}")
                                failed_batches += 1
                                break

                    except Exception as e:
                        logger.error(f"Error fetching quotes for batch {batch_num}: {e}")
                        failed_batches += 1
                        break

        except Exception as e:
            logger.error(f"Error in get_quotes: {e}")

        # Performance summary
        total_time = time.time() - start_time
        success_rate = (successful_batches / total_batches * 100) if total_batches > 0 else 0

        logger.info(f"Quote fetching completed: {len(market_data)}/{len(symbols)} symbols "
                   f"({success_rate:.1f}% batch success rate) in {total_time:.1f}s")

        if failed_batches > 0:
            logger.warning(f"Failed to fetch {failed_batches} batches due to rate limiting or errors")

        return market_data

    def _process_quotes_data(self, quotes_data: any, batch_symbols: List[str], market_data: Dict[str, MarketData]) -> None:
        """
        Optimized processing of quotes data from Fyers API response.

        Args:
            quotes_data: Response data from Fyers API
            batch_symbols: List of symbols in the current batch
            market_data: Dictionary to store parsed market data
        """
        try:
            # Handle case where quotes_data might be a list instead of dict
            if isinstance(quotes_data, list):
                # If it's a list, try to match symbols by position
                for idx, symbol_data in enumerate(quotes_data):
                    if idx < len(batch_symbols) and isinstance(symbol_data, dict):
                        symbol = batch_symbols[idx]
                        try:
                            market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                        except Exception as e:
                            logger.debug(f"Failed to parse data for {symbol}: {e}")

            elif isinstance(quotes_data, dict):
                # Original logic for dict response - optimized with batch processing
                for symbol in batch_symbols:
                    symbol_data = quotes_data.get(symbol)
                    if symbol_data:
                        try:
                            market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                        except Exception as e:
                            logger.debug(f"Failed to parse data for {symbol}: {e}")
                    # Removed debug logging for missing symbols to reduce noise

            else:
                logger.warning(f"Unexpected quotes_data format: {type(quotes_data)}")

        except Exception as e:
            logger.error(f"Error processing quotes data: {e}")
    
    def _parse_market_data(self, symbol: str, data: Dict[str, Any]) -> MarketData:
        """
        Parse market data from Fyers API response.
        
        Args:
            symbol: Symbol string
            data: Market data from Fyers API
            
        Returns:
            MarketData object
        """
        try:
            # If 'v' key exists and is a dict, use its fields for OHLC etc.
            v = data.get('v', {})
            if isinstance(v, dict) and v:
                ltp = float(v.get('lp', 0))
                volume = int(v.get('volume', 0))
                open_price = float(v.get('open_price', 0))
                high = float(v.get('high_price', 0))
                low = float(v.get('low_price', 0))
                close = float(v.get('lp', 0))  # Fyers does not provide close, use ltp
                prev_close = float(v.get('prev_close_price', open_price))
                change = ltp - prev_close if prev_close > 0 else 0
                change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            else:
                # Fallback to old logic if 'v' is not present
                ltp = float(data.get("lp", 0))
                volume_data = data.get("v", 0)
                if isinstance(volume_data, dict):
                    volume = int(volume_data.get("volume", 0))
                else:
                    volume = int(volume_data) if volume_data else 0
                open_price = float(data.get("o", 0))
                high = float(data.get("h", 0))
                low = float(data.get("l", 0))
                close = float(data.get("c", 0))
                prev_close = float(data.get("prev_close", close))
                change = ltp - prev_close if prev_close > 0 else 0
                change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            return MarketData(
                symbol=symbol,
                ltp=ltp,
                volume=volume,
                open_price=open_price,
                high=high,
                low=low,
                close=close,
                prev_close=prev_close,
                change=change,
                change_percent=change_percent
            )
        except Exception as e:
            logger.error(f"Error parsing market data for {symbol}: {e}")
            # Return default MarketData with zero values
            return MarketData(
                symbol=symbol,
                ltp=0.0,
                volume=0,
                open_price=0.0,
                high=0.0,
                low=0.0,
                close=0.0,
                prev_close=0.0,
                change=0.0,
                change_percent=0.0
            )
    
    def get_single_quote(self, symbol: str) -> Optional[MarketData]:
        """
        Get market quote for a single symbol.
        
        Args:
            symbol: Symbol string in NSE format
            
        Returns:
            MarketData object or None if failed
        """
        quotes = self.get_quotes([symbol])
        return quotes.get(symbol)

    def get_market_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """
        Alias for get_quotes method for compatibility.

        Args:
            symbols: List of symbol strings in NSE format

        Returns:
            Dictionary mapping symbol to MarketData object
        """
        return self.get_quotes(symbols)

    def get_quotes_optimized(self, symbols: List[str], chunk_size: int = 1000) -> Dict[str, MarketData]:
        """
        Get market quotes with memory optimization for large symbol lists.
        Processes symbols in chunks to reduce memory usage.

        Args:
            symbols: List of symbol strings in NSE format
            chunk_size: Number of symbols to process in each chunk

        Returns:
            Dictionary mapping symbol to MarketData object
        """
        if not symbols:
            return {}

        if len(symbols) <= chunk_size:
            return self.get_quotes(symbols)

        logger.info(f"Processing {len(symbols)} symbols in chunks of {chunk_size}")

        all_market_data = {}
        total_chunks = (len(symbols) + chunk_size - 1) // chunk_size

        for i in range(0, len(symbols), chunk_size):
            chunk_symbols = symbols[i:i + chunk_size]
            chunk_num = i // chunk_size + 1

            logger.info(f"Processing chunk {chunk_num}/{total_chunks} ({len(chunk_symbols)} symbols)")

            chunk_data = self.get_quotes(chunk_symbols)
            all_market_data.update(chunk_data)

            # Memory cleanup hint
            del chunk_data

        logger.info(f"Completed processing all chunks: {len(all_market_data)} symbols retrieved")
        return all_market_data

    def is_authenticated(self) -> bool:
        """
        Check if client is authenticated.
        
        Returns:
            True if authenticated, False otherwise
        """
        return self.fyers_api is not None and self.access_token is not None
    
    def get_historical_data(self, symbol: str, interval: int, days_to_fetch: int, start_date: datetime = None, end_date: datetime = None) -> List[OHLCData]:
        """
        Get historical OHLC data for a symbol with enhanced rate limiting.

        Args:
            symbol: Symbol string in NSE format
            interval: Timeframe interval (1, 3, 5, 15, 30, 60 for minutes, 1D for daily)
            days_to_fetch: Number of days to fetch historical data (used if start_date/end_date not provided)
            start_date: Specific start date for data (optional)
            end_date: Specific end date for data (optional)

        Returns:
            List of OHLCData objects
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return []

        config = get_config()
        min_delay = config.rate_limit.min_delay_seconds
        max_retries = config.rate_limit.max_retries
        retry_backoff = config.rate_limit.retry_backoff

        # Enhanced rate limiting for historical data
        historical_delay = max(min_delay, 0.5)  # Minimum 0.5 seconds for historical data

        attempt = 0
        while attempt <= max_retries:
            try:
                # Calculate date range
                if start_date and end_date:
                    # Use provided dates
                    fetch_start_date = start_date
                    fetch_end_date = end_date

                    # Validate date range for 1-minute data (max 100 days)
                    date_diff = (fetch_end_date - fetch_start_date).days
                    if interval <= 60 and date_diff > 100:  # For minute-level data
                        logger.error(f"Date range too large for 1-minute data: {date_diff} days (max 100 days)")
                        return []
                else:
                    # Use days_to_fetch from current date
                    fetch_end_date = datetime.now()
                    fetch_start_date = fetch_end_date - timedelta(days=days_to_fetch)

                # Convert to YYYY-MM-DD format as required by Fyers API when date_format=1
                from_date = fetch_start_date.strftime('%Y-%m-%d')
                to_date = fetch_end_date.strftime('%Y-%m-%d')

                # Map interval to Fyers format
                fyers_interval = str(interval) if interval != 1440 else "1D"
                if str(interval) in INTERVAL_MAP:
                    fyers_interval = INTERVAL_MAP[str(interval)]

                logger.info(f"FyersClient: Fetching historical data for {symbol} with interval {interval} (mapped: {fyers_interval}), days: {days_to_fetch}")

                # Prepare request data
                data = {
                    "symbol": symbol,
                    "resolution": fyers_interval,
                    "date_format": "1",  # Date string format (YYYY-MM-DD)
                    "range_from": from_date,
                    "range_to": to_date,
                    "cont_flag": "1"
                }

                # Apply rate limiting before request
                time.sleep(historical_delay)

                # Get historical data from Fyers API
                response = self.fyers_api.history(data)

                if response and response.get("code") == 200:
                    candles = response.get("candles", [])
                    ohlc_data = []

                    for candle in candles:
                        if len(candle) >= 6:  # timestamp, open, high, low, close, volume
                            # Convert Unix timestamp to datetime with timezone conversion
                            # Fyers returns UTC timestamps, convert to Asia/Kolkata and remove timezone info
                            utc_dt = datetime.fromtimestamp(int(candle[0]), tz=pytz.UTC)
                            kolkata_dt = utc_dt.astimezone(pytz.timezone('Asia/Kolkata'))
                            naive_dt = kolkata_dt.replace(tzinfo=None)
                            # Ensure naive_dt is a naive datetime object (no tzinfo)
                            if naive_dt.tzinfo is not None:
                                naive_dt = naive_dt.replace(tzinfo=None)
                                
                            ohlc_data.append(OHLCData(
                                timestamp=naive_dt,
                                open=float(candle[1]),
                                high=float(candle[2]),
                                low=float(candle[3]),
                                close=float(candle[4]),
                                volume=int(candle[5])
                            ))

                    logger.info(f"Successfully fetched {len(ohlc_data)} OHLC data points for {symbol}")
                    return ohlc_data

                elif response and response.get("code") == 429:
                    attempt += 1
                    # Exponential backoff with jitter for rate limiting
                    backoff_time = retry_backoff * (2 ** (attempt - 1)) + (attempt * 0.1)
                    logger.warning(f"Rate limit hit for {symbol}, attempt {attempt}/{max_retries}. "
                                 f"Retrying after {backoff_time:.1f} seconds...")
                    time.sleep(backoff_time)

                elif response and response.get("code") == -99:
                    logger.error(f"Invalid symbol or bad request for {symbol}: {response}")
                    return []

                else:
                    logger.error(f"Failed to get historical data for {symbol}: {response}")
                    if attempt < max_retries:
                        attempt += 1
                        time.sleep(retry_backoff * attempt)
                    else:
                        return []

            except Exception as e:
                logger.error(f"Error fetching historical data for {symbol}: {e}")
                if attempt < max_retries:
                    attempt += 1
                    time.sleep(retry_backoff * attempt)
                else:
                    return []

        logger.error(f"Exceeded max retries for {symbol} due to rate limiting.")
        return []

    def bulk_get_historical_data(
        self,
        symbols: List[str],
        interval: int,
        days_to_fetch: int,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, List[OHLCData]]:
        """
        Get historical data for multiple symbols with optimized rate limiting.

        Args:
            symbols: List of symbols to fetch
            interval: Timeframe interval
            days_to_fetch: Number of days to fetch
            progress_callback: Optional callback for progress updates

        Returns:
            Dictionary mapping symbol to OHLC data list
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return {}

        config = get_config()
        min_delay = max(config.rate_limit.min_delay_seconds, 0.5)  # Minimum 0.5s for historical

        results = {}
        total_symbols = len(symbols)

        logger.info(f"Starting bulk historical data fetch for {total_symbols} symbols")

        for i, symbol in enumerate(symbols):
            try:
                # Progress tracking
                if progress_callback:
                    progress_callback(i, total_symbols, symbol)

                logger.info(f"Fetching historical data for {symbol} ({i+1}/{total_symbols})")

                # Fetch data for this symbol
                ohlc_data = self.get_historical_data(symbol, interval, days_to_fetch)
                results[symbol] = ohlc_data

                # Log result
                if ohlc_data:
                    logger.info(f"✓ {symbol}: {len(ohlc_data)} records")
                else:
                    logger.warning(f"✗ {symbol}: No data received")

                # Rate limiting between symbols
                if i < total_symbols - 1:  # Don't sleep after last symbol
                    time.sleep(min_delay)

            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
                results[symbol] = []

        successful = sum(1 for data in results.values() if data)
        logger.info(f"Bulk fetch completed: {successful}/{total_symbols} symbols successful")

        return results
